<?php

namespace App\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CancellationPolicyResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @return array<string, mixed>
     */
    public function toArray(Request $request): array
    {
        return [
            'id' => $this->id,
            'name_en' => $this->name_en,
            'name_ar' => $this->name_ar,
            'name' => $this->localized_name,
            'description_en' => $this->description_en,
            'description_ar' => $this->description_ar,
            'description' => $this->localized_description,
            'policy_type' => $this->policy_type,
            'duration_type' => $this->duration_type,
            'cancellation_window_hours' => $this->cancellation_window_hours,
            'refund_percentage' => $this->refund_percentage,
            'booking_window_hours' => $this->booking_window_hours,
            'minimum_notice_hours' => $this->minimum_notice_hours,
            'service_fee_refundable' => $this->service_fee_refundable,
            'cleaning_fee_refundable' => $this->cleaning_fee_refundable,
            'is_active' => $this->is_active,
            'order' => $this->order,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
