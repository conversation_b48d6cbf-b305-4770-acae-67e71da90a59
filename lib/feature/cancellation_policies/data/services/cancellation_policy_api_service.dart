import 'package:gather_point/core/network/dio_consumer.dart';
import 'package:gather_point/feature/cancellation_policies/data/models/cancellation_policy_model.dart';

class CancellationPolicyApiService {
  final DioConsumer _dioConsumer;

  CancellationPolicyApiService(this._dioConsumer);

  /// Get all active cancellation policies
  Future<List<CancellationPolicyModel>> getCancellationPolicies() async {
    try {
      final response = await _dioConsumer.get('/api/cancellation-policies');

      if (response['success'] == true) {
        final List<dynamic> data = response['data'] ?? [];
        return data.map((json) => CancellationPolicyModel.fromJson(json)).toList();
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch cancellation policies');
      }
    } catch (e) {
      throw Exception('Failed to fetch cancellation policies: ${e.toString()}');
    }
  }

  /// Get a specific cancellation policy by ID
  Future<CancellationPolicyModel> getCancellationPolicy(int id) async {
    try {
      final response = await _dioConsumer.get('/api/cancellation-policies/$id');

      if (response['success'] == true) {
        return CancellationPolicyModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to fetch cancellation policy');
      }
    } catch (e) {
      throw Exception('Failed to fetch cancellation policy: ${e.toString()}');
    }
  }

  /// Create a new cancellation policy (Admin only)
  Future<CancellationPolicyModel> createCancellationPolicy({
    required String nameEn,
    required String nameAr,
    required String descriptionEn,
    required String descriptionAr,
    required String policyType,
    required String durationType,
    required int cancellationWindowHours,
    required double refundPercentage,
    int? bookingWindowHours,
    int? minimumNoticeHours,
    bool serviceFeeRefundable = true,
    bool cleaningFeeRefundable = true,
    bool isActive = true,
    int order = 0,
  }) async {
    try {
      final data = {
        'name_en': nameEn,
        'name_ar': nameAr,
        'description_en': descriptionEn,
        'description_ar': descriptionAr,
        'policy_type': policyType,
        'duration_type': durationType,
        'cancellation_window_hours': cancellationWindowHours,
        'refund_percentage': refundPercentage,
        'booking_window_hours': bookingWindowHours,
        'minimum_notice_hours': minimumNoticeHours,
        'service_fee_refundable': serviceFeeRefundable,
        'cleaning_fee_refundable': cleaningFeeRefundable,
        'is_active': isActive,
        'order': order,
      };

      final response = await _dioConsumer.post(
        '/api/cancellation-policies',
        data: data,
      );

      if (response['success'] == true) {
        return CancellationPolicyModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to create cancellation policy');
      }
    } catch (e) {
      throw Exception('Failed to create cancellation policy: ${e.toString()}');
    }
  }

  /// Update a cancellation policy (Admin only)
  Future<CancellationPolicyModel> updateCancellationPolicy({
    required int id,
    String? nameEn,
    String? nameAr,
    String? descriptionEn,
    String? descriptionAr,
    String? policyType,
    String? durationType,
    int? cancellationWindowHours,
    double? refundPercentage,
    int? bookingWindowHours,
    int? minimumNoticeHours,
    bool? serviceFeeRefundable,
    bool? cleaningFeeRefundable,
    bool? isActive,
    int? order,
  }) async {
    try {
      final data = <String, dynamic>{};
      
      if (nameEn != null) data['name_en'] = nameEn;
      if (nameAr != null) data['name_ar'] = nameAr;
      if (descriptionEn != null) data['description_en'] = descriptionEn;
      if (descriptionAr != null) data['description_ar'] = descriptionAr;
      if (policyType != null) data['policy_type'] = policyType;
      if (durationType != null) data['duration_type'] = durationType;
      if (cancellationWindowHours != null) data['cancellation_window_hours'] = cancellationWindowHours;
      if (refundPercentage != null) data['refund_percentage'] = refundPercentage;
      if (bookingWindowHours != null) data['booking_window_hours'] = bookingWindowHours;
      if (minimumNoticeHours != null) data['minimum_notice_hours'] = minimumNoticeHours;
      if (serviceFeeRefundable != null) data['service_fee_refundable'] = serviceFeeRefundable;
      if (cleaningFeeRefundable != null) data['cleaning_fee_refundable'] = cleaningFeeRefundable;
      if (isActive != null) data['is_active'] = isActive;
      if (order != null) data['order'] = order;

      final response = await _dioConsumer.put(
        '/api/cancellation-policies/$id',
        data: data,
      );

      if (response['success'] == true) {
        return CancellationPolicyModel.fromJson(response['data']);
      } else {
        throw Exception(response['message'] ?? 'Failed to update cancellation policy');
      }
    } catch (e) {
      throw Exception('Failed to update cancellation policy: ${e.toString()}');
    }
  }

  /// Delete a cancellation policy (Admin only)
  Future<bool> deleteCancellationPolicy(int id) async {
    try {
      final response = await _dioConsumer.delete('/api/cancellation-policies/$id');

      return response['success'] == true;
    } catch (e) {
      throw Exception('Failed to delete cancellation policy: ${e.toString()}');
    }
  }
}
