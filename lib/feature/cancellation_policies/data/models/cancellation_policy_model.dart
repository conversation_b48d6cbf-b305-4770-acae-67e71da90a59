class CancellationPolicyModel {
  final int id;
  final String nameEn;
  final String nameAr;
  final String name;
  final String descriptionEn;
  final String descriptionAr;
  final String description;
  final String policyType;
  final int cancellationWindowHours;
  final double refundPercentage;
  final bool serviceFeeRefundable;
  final bool cleaningFeeRefundable;
  final bool isActive;
  final int order;
  final String createdAt;
  final String updatedAt;

  const CancellationPolicyModel({
    required this.id,
    required this.nameEn,
    required this.nameAr,
    required this.name,
    required this.descriptionEn,
    required this.descriptionAr,
    required this.description,
    required this.policyType,
    required this.cancellationWindowHours,
    required this.refundPercentage,
    required this.serviceFeeRefundable,
    required this.cleaningFeeRefundable,
    required this.isActive,
    required this.order,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CancellationPolicyModel.fromJson(Map<String, dynamic> json) {
    return CancellationPolicyModel(
      id: json['id'] ?? 0,
      nameEn: json['name_en'] ?? '',
      nameAr: json['name_ar'] ?? '',
      name: json['name'] ?? '',
      descriptionEn: json['description_en'] ?? '',
      descriptionAr: json['description_ar'] ?? '',
      description: json['description'] ?? '',
      policyType: json['policy_type'] ?? '',
      cancellationWindowHours: json['cancellation_window_hours'] ?? 0,
      refundPercentage: double.tryParse(json['refund_percentage']?.toString() ?? '0') ?? 0.0,
      serviceFeeRefundable: json['service_fee_refundable'] ?? false,
      cleaningFeeRefundable: json['cleaning_fee_refundable'] ?? false,
      isActive: json['is_active'] ?? false,
      order: json['order'] ?? 0,
      createdAt: json['created_at'] ?? '',
      updatedAt: json['updated_at'] ?? '',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name_en': nameEn,
      'name_ar': nameAr,
      'name': name,
      'description_en': descriptionEn,
      'description_ar': descriptionAr,
      'description': description,
      'policy_type': policyType,
      'cancellation_window_hours': cancellationWindowHours,
      'refund_percentage': refundPercentage,
      'service_fee_refundable': serviceFeeRefundable,
      'cleaning_fee_refundable': cleaningFeeRefundable,
      'is_active': isActive,
      'order': order,
      'created_at': createdAt,
      'updated_at': updatedAt,
    };
  }

  CancellationPolicyModel copyWith({
    int? id,
    String? nameEn,
    String? nameAr,
    String? name,
    String? descriptionEn,
    String? descriptionAr,
    String? description,
    String? policyType,
    int? cancellationWindowHours,
    double? refundPercentage,
    bool? serviceFeeRefundable,
    bool? cleaningFeeRefundable,
    bool? isActive,
    int? order,
    String? createdAt,
    String? updatedAt,
  }) {
    return CancellationPolicyModel(
      id: id ?? this.id,
      nameEn: nameEn ?? this.nameEn,
      nameAr: nameAr ?? this.nameAr,
      name: name ?? this.name,
      descriptionEn: descriptionEn ?? this.descriptionEn,
      descriptionAr: descriptionAr ?? this.descriptionAr,
      description: description ?? this.description,
      policyType: policyType ?? this.policyType,
      cancellationWindowHours: cancellationWindowHours ?? this.cancellationWindowHours,
      refundPercentage: refundPercentage ?? this.refundPercentage,
      serviceFeeRefundable: serviceFeeRefundable ?? this.serviceFeeRefundable,
      cleaningFeeRefundable: cleaningFeeRefundable ?? this.cleaningFeeRefundable,
      isActive: isActive ?? this.isActive,
      order: order ?? this.order,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Get formatted cancellation window text
  String get formattedCancellationWindow {
    if (cancellationWindowHours < 24) {
      return '$cancellationWindowHours hours';
    } else {
      final days = (cancellationWindowHours / 24).round();
      return '$days day${days > 1 ? 's' : ''}';
    }
  }

  /// Get policy type display name
  String get policyTypeDisplayName {
    switch (policyType) {
      case 'flexible':
        return 'Flexible';
      case 'moderate':
        return 'Moderate';
      case 'strict':
        return 'Strict';
      case 'super_strict':
        return 'Super Strict';
      case 'custom':
        return 'Custom';
      default:
        return 'Unknown';
    }
  }
}
